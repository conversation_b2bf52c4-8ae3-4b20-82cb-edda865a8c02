import { BWPParser } from '../../src/parsers/bwp-parser';
import { BWPJavaGenerator } from '../../src/generators/bwp-java-generator';
import { JavaGenerationOptions } from '../../src/types';
import * as path from 'path';

describe('BWP Integration Tests', () => {
  let parser: BWPParser;
  let generator: BWPJavaGenerator;
  let options: JavaGenerationOptions;

  beforeEach(() => {
    parser = new BWPParser();
    options = {
      packageName: 'com.example.movies',
      outputDir: '/output',
      useJSR303Validation: true,
      useLombok: false,
      useJacksonAnnotations: true,
      includeConstructors: true,
      includeToString: true
    };
    generator = new BWPJavaGenerator(options);
  });

  describe('Real BWP File Processing', () => {
    it('should parse and generate Java code for SearchMovies.bwp', async () => {
      const bwpFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');

      try {
        // Parse the BWP file
        const parsedProcess = await parser.parseBWP(bwpFilePath);
        
        // Verify basic parsing
        expect(parsedProcess).toBeDefined();
        expect(parsedProcess.name).toBe('SearchMovies');
        expect(parsedProcess.namespace).toBe('http://xmlns.example.com/20190722212639');
        
        // Verify process info
        expect(parsedProcess.processInfo.callable).toBe(true);
        expect(parsedProcess.processInfo.stateless).toBe(true);
        
        // Verify interface
        expect(parsedProcess.interface.inputType).toBe('moviesGetParameters');
        expect(parsedProcess.interface.outputType).toBe('OMDBSearchElement');
        
        // Verify variables
        expect(parsedProcess.variables.length).toBeGreaterThan(0);
        const startVar = parsedProcess.variables.find(v => v.name === 'Start');
        expect(startVar).toBeDefined();
        expect(startVar?.parameterType).toBe('in');
        
        // Verify partner links
        expect(parsedProcess.partnerLinks.length).toBeGreaterThan(0);
        const moviesLink = parsedProcess.partnerLinks.find(p => p.name === 'movies');
        expect(moviesLink).toBeDefined();
        expect(moviesLink?.restBinding).toBeDefined();
        
        // Verify REST endpoints
        expect(parsedProcess.restEndpoints.length).toBeGreaterThan(0);
        const endpoint = parsedProcess.restEndpoints[0];
        expect(endpoint.method).toBe('GET');
        expect(endpoint.path).toBe('/movies');
        
        // Generate Java code
        const controllerCode = generator.generateController(parsedProcess);
        const serviceCode = generator.generateService(parsedProcess);
        
        // Verify controller generation
        expect(controllerCode).toContain('package com.example.movies');
        expect(controllerCode).toContain('@RestController');
        expect(controllerCode).toContain('public class SearchMoviesController');
        expect(controllerCode).toContain('@GetMapping("/movies")');
        expect(controllerCode).toContain('ResponseEntity<OMDBSearchElement>');
        
        // Verify service generation
        expect(serviceCode).toContain('package com.example.movies');
        expect(serviceCode).toContain('@Service');
        expect(serviceCode).toContain('public class SearchMoviesService');
        expect(serviceCode).toContain('RestTemplate');
        expect(serviceCode).toContain('getForObject');
        
        console.log('Generated Controller:');
        console.log(controllerCode);
        console.log('\nGenerated Service:');
        console.log(serviceCode);
        
      } catch (error) {
        console.error('Error processing BWP file:', error);
        throw error;
      }
    });

    it('should generate complete Java project with XSD models for SearchMovies.bwp', async () => {
      const bwpFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');
      const xsdDirectory = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');

      try {
        // Parse the BWP file
        const parsedProcess = await parser.parseBWP(bwpFilePath);

        // Generate XSD models
        const { XSDJavaModelGenerator } = require('../../src/generators/xsd-java-model-generator');
        const modelGenerator = new XSDJavaModelGenerator({
          packageName: 'com.example.movies.model',
          outputDir: './temp-output',
          useJSR303Validation: true,
          useLombok: false,
          useJacksonAnnotations: true,
          includeConstructors: true,
          includeToString: true
        });

        const modelResult = await modelGenerator.generateAllModelsForBWP(xsdDirectory);

        // Verify model generation
        expect(modelResult.allClasses.length).toBeGreaterThan(0);
        expect(modelResult.inputOutputMapping.size).toBeGreaterThan(0);

        // Verify specific models exist
        expect(modelResult.inputOutputMapping.has('MoviesGetParameters')).toBe(true);
        expect(modelResult.inputOutputMapping.has('OMDBSearchElement')).toBe(true);

        // Generate BWP Java code
        const controllerCode = generator.generateController(parsedProcess);
        const serviceCode = generator.generateService(parsedProcess);

        // Verify integration - controller should reference the output model class
        // Note: Input parameters are currently handled as query params, not as request body
        expect(controllerCode).toContain('OMDBSearchElement');
        expect(serviceCode).toContain('OMDBSearchElement');

        // Verify that the XSD models were generated and are available
        const inputModel = modelResult.inputOutputMapping.get('MoviesGetParameters');
        const outputModel = modelResult.inputOutputMapping.get('OMDBSearchElement');
        expect(inputModel).toBeDefined();
        expect(outputModel).toBeDefined();
        expect(inputModel?.fields.length).toBeGreaterThan(0);

        console.log('Input model fields:', inputModel?.fields.map((f: any) => f.name));
        console.log('Output model fields:', outputModel?.fields.map((f: any) => f.name));
        console.log('Output model is root element:', outputModel?.isRootElement);

        // Note: Some XSD models might be root elements without fields (wrapper elements)
        // So we'll check if it's a root element or has fields
        expect(outputModel?.isRootElement || (outputModel?.fields.length || 0) > 0).toBe(true);

        console.log('Successfully generated complete Java project');
        console.log('Generated models:', modelResult.allClasses.length);
        console.log('Model types available:', Array.from(modelResult.inputOutputMapping.keys()).slice(0, 10));

      } catch (error) {
        console.error('Error in complete integration test:', error);
        throw error;
      }
    });

    it('should handle BWP file with minimal structure', async () => {
      // Create a minimal BWP content for testing
      const minimalBWP = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process exitOnStandardFault="no"
            name="test.MinimalProcess"
            targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true" stateless="true" type="IT" modifiers="public"/>
            <tibex:ProcessInterface
                input="{http://test.com}TestInput"
                output="{http://test.com}TestOutput"/>
            <bpws:variables>
                <bpws:variable element="ns:TestInput" name="Start"
                    sca-bpel:internal="true" tibex:parameter="in"/>
                <bpws:variable element="ns:TestOutput" name="End"
                    sca-bpel:internal="true" tibex:parameter="out"/>
            </bpws:variables>
        </bpws:process>`;

      // 创建临时文件进行真实测试
      const tempBWPPath = path.join(__dirname, '../temp-minimal.bwp');
      require('fs').writeFileSync(tempBWPPath, minimalBWP);

      try {
        const parsedProcess = await parser.parseBWP(tempBWPPath);

        expect(parsedProcess).toBeDefined();
        expect(parsedProcess.name).toBe('MinimalProcess');
        expect(parsedProcess.interface.inputType).toBe('TestInput');
        expect(parsedProcess.interface.outputType).toBe('TestOutput');
        expect(parsedProcess.variables.length).toBe(2);
        expect(parsedProcess.partnerLinks.length).toBe(0);
        expect(parsedProcess.restEndpoints.length).toBe(0);

        // Should still be able to generate basic Java code
        const controllerCode = generator.generateController(parsedProcess);
        const serviceCode = generator.generateService(parsedProcess);

        expect(controllerCode).toContain('public class MinimalProcessController');
        expect(serviceCode).toContain('public class MinimalProcessService');
      } finally {
        // 清理临时文件
        if (require('fs').existsSync(tempBWPPath)) {
          require('fs').unlinkSync(tempBWPPath);
        }
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid XML gracefully', async () => {
      const invalidXML = 'This is not valid XML';
      const tempInvalidPath = path.join(__dirname, '../temp-invalid.bwp');

      require('fs').writeFileSync(tempInvalidPath, invalidXML);

      try {
        await expect(parser.parseBWP(tempInvalidPath)).rejects.toThrow('Failed to parse BWP XML');
      } finally {
        if (require('fs').existsSync(tempInvalidPath)) {
          require('fs').unlinkSync(tempInvalidPath);
        }
      }
    });

    it('should handle missing process interface', async () => {
      const noInterfaceBWP = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.NoInterface" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true"/>
        </bpws:process>`;

      const tempNoInterfacePath = path.join(__dirname, '../temp-nointerface.bwp');
      require('fs').writeFileSync(tempNoInterfacePath, noInterfaceBWP);

      try {
        await expect(parser.parseBWP(tempNoInterfacePath)).rejects.toThrow('No process interface found');
      } finally {
        if (require('fs').existsSync(tempNoInterfacePath)) {
          require('fs').unlinkSync(tempNoInterfacePath);
        }
      }
    });
  });

  describe('Code Generation Quality', () => {
    it('should generate valid Java syntax', async () => {
      const testBWP = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.QualityTest" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true" stateless="true"/>
            <tibex:ProcessInterface 
                input="{http://test.com}QualityInput" 
                output="{http://test.com}QualityOutput"/>
            <bpws:partnerLinks>
                <bpws:partnerLink name="testAPI" partnerLinkType="ns:TestLinkType" partnerRole="use">
                    <tibex:ReferenceBinding>
                        <tibex:binding>
                            <bwbinding:BWBaseBinding xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding">
                                <referenceBinding name="testAPI" xsi:type="scact:Reference" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType">
                                    <scaext:binding basePath="/api" path="/test" 
                                        xsi:type="rest:RestReferenceBinding" xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest">
                                        <operation httpMethod="POST" operationName="createTest">
                                            <parameters>
                                                <parameterMapping dataType="string" 
                                                    parameterName="test_id" 
                                                    parameterType="Path" required="true"/>
                                                <parameterMapping dataType="string" 
                                                    parameterName="filter_value" 
                                                    parameterType="Query" required="false"/>
                                            </parameters>
                                            <clientFormat>json</clientFormat>
                                        </operation>
                                    </scaext:binding>
                                </referenceBinding>
                            </bwbinding:BWBaseBinding>
                        </tibex:binding>
                    </tibex:ReferenceBinding>
                </bpws:partnerLink>
            </bpws:partnerLinks>
        </bpws:process>`;

      jest.doMock('../../src/utils/file-utils', () => ({
        readFileContent: jest.fn().mockReturnValue(testBWP)
      }));

      const parsedProcess = await parser.parseBWP('/test/quality.bwp');
      const controllerCode = generator.generateController(parsedProcess);
      const serviceCode = generator.generateService(parsedProcess);
      
      // Check for proper Java syntax elements
      expect(controllerCode).toMatch(/package\s+[\w.]+;/);
      expect(controllerCode).toMatch(/import\s+[\w.]+;/);
      expect(controllerCode).toMatch(/public\s+class\s+\w+Controller\s*\{/);
      expect(controllerCode).toMatch(/@RestController/);
      expect(controllerCode).toMatch(/@PostMapping\("\/test"\)/);
      expect(controllerCode).toMatch(/@PathVariable\("test_id"\)\s+String\s+testId/);
      expect(controllerCode).toMatch(/@RequestParam\("filter_value"\)\s+String\s+filterValue/);
      
      expect(serviceCode).toMatch(/package\s+[\w.]+;/);
      expect(serviceCode).toMatch(/public\s+class\s+\w+Service\s*\{/);
      expect(serviceCode).toMatch(/@Service/);
      expect(serviceCode).toMatch(/RestTemplate/);
      expect(serviceCode).toMatch(/postForObject/);
    });
  });
});
